<!DOCTYPE html>
<!-- HTML5文档类型声明 -->
<html lang="en">
<!-- HTML根元素，设置语言为英语 -->
<head>
    <!-- 头部信息区域 -->
    <meta charset="UTF-8"> <!-- 字符编码设置为UTF-8，支持各种语言字符 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- 响应式视口设置，适配移动设备 -->
    <title>OTA Order Converter - 订单识别</title> <!-- 页面标题：OTA订单转换器 -->
    <style>
        /* 基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f9fafb;
            color: #111827;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
        }
        
        header {
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 0;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo-container img {
            height: 2rem;
            margin-right: 0.75rem;
        }
        
        h1 {
            font-size: 1.25rem;
            font-weight: 500;
            margin: 0;
        }
        
        main {
            flex: 1;
            padding: 1.5rem 0;
        }
        
        .card {
            background-color: white;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 500;
            margin: 0;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.25rem;
        }
        
        .form-input, textarea {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            background-color: #fff;
            color: #111827;
            font-size: 1rem;
            line-height: 1.5;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .form-input:focus, textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
        }
        
        textarea {
            min-height: 200px;
            resize: vertical;
        }
        
        .btn {
            display: inline-block;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -webkit-user-select: none;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 0.375rem;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            cursor: pointer;
        }
        
        .btn-primary {
            color: #fff;
            background-color: #3b82f6;
            border-color: #3b82f6;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        
        .btn-gray {
            color: #fff;
            background-color: #6b7280;
            border-color: #6b7280;
        }
        
        .btn-gray:hover {
            background-color: #4b5563;
            border-color: #4b5563;
        }
        
        .button-group {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .flex-1 {
            flex: 1;
        }
        
        footer {
            margin-top: auto;
            padding: 1rem 0;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .notification {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            background-color: #10b981;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            z-index: 50;
        }
        
        .notification.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="notification" id="notification"></div>
    
    <header>
        <div class="container header-content">
            <div class="logo-container">
                <img src="images/logo.svg" alt="Logo">
                <h1 data-i18n="app-title">OTA Order Converter</h1>
            </div>
            <div class="lang-selector">
                <select id="language-selector" class="form-input language-selector" aria-label="选择语言 Select language">
                    <option value="zh">中文</option>
                    <option value="en">English</option>
                    <option value="jp">日本語</option>
                    <option value="ko">한국어</option>
                    <option value="jingge">Jing Ge OTA</option>
                </select>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title" data-i18n="order-recognition">OTA订单识别</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label for="raw-order" class="form-label" data-i18n="paste-original-order">请粘贴原始订单数据</label>
                    <textarea id="raw-order" data-i18n-placeholder="paste-order-data-hint" placeholder="请粘贴OTA订单数据，系统将自动识别订单类型并跳转到对应处理页面..."></textarea>
                </div>
                <div class="button-group">
                    <button id="identify-btn" class="btn btn-primary flex-1" data-i18n="identify-order">识别订单</button>
                    <button id="reset-btn" class="btn btn-gray" data-i18n="reset">重置</button>
                </div>
                <div class="button-group" style="margin-top: 1.5rem;">
                    <a href="fliggy.html" class="btn btn-primary" style="flex: 1;" data-i18n="go-to-fliggy">直接前往Fliggy处理</a>
                    <a href="kkday.html" class="btn btn-primary" style="flex: 1;" data-i18n="go-to-kkday">直接前往Kkday处理</a>
                    <a href="Jing Ge.html" class="btn btn-primary" style="flex: 1;" data-i18n="go-to-jingge">直接前往Jing Ge OTA处理</a>
                    <a href="chong.html" class="btn btn-primary" style="flex: 1;" data-i18n="go-to-chong">直接前往Chong Dealer处理</a>
                    <a href="ctrip.html" class="btn btn-primary" style="flex: 1;" data-i18n="go-to-ctrip">直接前往携程专车处理</a>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            Built with <a href="https://flowith.net" target="_blank" rel="noopener" style="color: #3b82f6; text-decoration: none;">Flowith Oracle</a>.
        </div>
    </footer>

    <script>
        // 多语言支持
        const i18n = {
            zh: {
                // 标题和标签
                "app-title": "OTA订单转换器",
                "order-recognition": "OTA订单识别",
                "paste-original-order": "请粘贴原始订单数据",
                "paste-order-data-hint": "请粘贴OTA订单数据，系统将自动识别订单类型并跳转到对应处理页面...",
                // 按钮
                "identify-order": "识别订单",
                "reset": "重置",
                "go-to-fliggy": "直接前往Fliggy处理",
                "go-to-kkday": "直接前往Kkday处理",
                "go-to-jingge": "直接前往Jing Ge OTA处理",
                "go-to-chong": "直接前往Chong Dealer处理",
                "go-to-ctrip": "直接前往携程专车处理",
                // 通知
                "please-enter-data": "请先粘贴订单数据！",
                "identified-as-fliggy": "已识别为Fliggy订单，正在跳转...",
                "identified-as-kkday": "已识别为Kkday订单，正在跳转...",
                "identified-as-jingge": "已识别为Jing Ge OTA订单，正在跳转...",
                "identified-as-chong": "已识别为Chong Dealer订单，正在跳转...",
                "identified-as-ctrip": "已识别为携程专车订单，正在跳转...",
                "cannot-identify": "无法识别订单类型，请手动选择处理页面。",
                "returned-to-home": "已返回主页"
            },
            en: {
                // Titles and labels
                "app-title": "OTA Order Converter",
                "order-recognition": "OTA Order Recognition",
                "paste-original-order": "Please paste original order data",
                "paste-order-data-hint": "Please paste OTA order data, the system will automatically recognize the order type and redirect to the corresponding processing page...",
                // Buttons
                "identify-order": "Identify Order",
                "reset": "Reset",
                "go-to-fliggy": "Go to Fliggy Processing",
                "go-to-kkday": "Go to Kkday Processing",
                "go-to-jingge": "Go to Jing Ge OTA Processing",
                "go-to-chong": "Go to Chong Dealer Processing",
                "go-to-ctrip": "Go to Ctrip Car Processing",
                // Notifications
                "please-enter-data": "Please paste order data first!",
                "identified-as-fliggy": "Identified as Fliggy order, redirecting...",
                "identified-as-kkday": "Identified as Kkday order, redirecting...",
                "identified-as-jingge": "Identified as Jing Ge OTA order, redirecting...",
                "identified-as-chong": "Identified as Chong Dealer order, redirecting...",
                "identified-as-ctrip": "Identified as Ctrip Car order, redirecting...",
                "cannot-identify": "Cannot identify order type, please select processing page manually.",
                "returned-to-home": "Returned to home page"
            },
            jp: {
                // タイトルとラベル
                "app-title": "OTA注文コンバーター",
                "order-recognition": "OTA注文識別",
                "paste-original-order": "元注文データを貼り付けてください",
                "paste-order-data-hint": "OTA注文データを貼り付けてください。システムは自動的に注文タイプを認識し、対応する処理ページにリダイレクトします...",
                // ボタン
                "identify-order": "注文を識別",
                "reset": "リセット",
                "go-to-fliggy": "Fliggy処理ページへ",
                "go-to-kkday": "Kkday処理ページへ",
                "go-to-jingge": "Jing Ge OTA処理ページへ",
                "go-to-chong": "Chong Dealer処理ページへ",
                "go-to-ctrip": "Ctrip Car処理ページへ",
                // 通知
                "please-enter-data": "最初に注文データを貼り付けてください！",
                "identified-as-fliggy": "Fliggy注文として識別されました、リダイレクト中...",
                "identified-as-kkday": "Kkday注文として識別されました、リダイレクト中...",
                "identified-as-jingge": "Jing Ge OTA注文として識別されました、リダイレクト中...",
                "identified-as-chong": "Chong Dealer注文として識別されました、リダイレクト中...",
                "identified-as-ctrip": "Ctrip Car注文として識別されました、リダイレクト中...",
                "cannot-identify": "注文タイプを識別できません。手動で処理ページを選択してください。",
                "returned-to-home": "ホームページに戻りました"
            },
            ko: {
                // 제목 및 레이블
                "app-title": "OTA 주문 변환기",
                "order-recognition": "OTA 주문 인식",
                "paste-original-order": "원본 주문 데이터를 붙여넣으세요",
                "paste-order-data-hint": "OTA 주문 데이터를 붙여넣으세요. 시스템이 자동으로 주문 유형을 인식하고 해당 처리 페이지로 리디렉션합니다...",
                // 버튼
                "identify-order": "주문 식별",
                "reset": "재설정",
                "go-to-fliggy": "Fliggy 처리로 이동",
                "go-to-kkday": "Kkday 처리로 이동",
                "go-to-jingge": "Jing Ge OTA 처리로 이동",
                "go-to-chong": "Chong Dealer 처리로 이동",
                "go-to-ctrip": "Ctrip Car 처리로 이동",
                // 알림
                "please-enter-data": "주문 데이터를 먼저 붙여넣으세요!",
                "identified-as-fliggy": "Fliggy 주문으로 식별되었습니다. 리디렉션 중...",
                "identified-as-kkday": "Kkday 주문으로 식별되었습니다. 리디렉션 중...",
                "identified-as-jingge": "Jing Ge OTA 주문으로 식별되었습니다. 리디렉션 중...",
                "identified-as-chong": "Chong Dealer 주문으로 식별되었습니다. 리디렉션 중...",
                "identified-as-ctrip": "Ctrip Car 주문으로 식별되었습니다. 리디렉션 중...",
                "cannot-identify": "주문 유형을 식별할 수 없습니다. 수동으로 처리 페이지를 선택하세요.",
                "returned-to-home": "홈페이지로 돌아왔습니다"
            },
            jingge: {
                // 标题和标签
                "app-title": "Jing Ge OTA订单转换器",
                "order-recognition": "Jing Ge OTA订单识别",
                "paste-original-order": "请粘贴原始订单数据",
                "paste-order-data-hint": "请粘贴Jing Ge OTA订单数据，系统将自动识别订单类型并跳转到对应处理页面...",
                // 按钮
                "identify-order": "识别订单",
                "reset": "重置",
                "go-to-fliggy": "直接前往Fliggy处理",
                "go-to-kkday": "直接前往Kkday处理",
                "go-to-jingge": "直接前往Jing Ge OTA处理",
                "go-to-chong": "直接前往Chong Dealer处理",
                "go-to-ctrip": "直接前往携程专车处理",
                // 通知
                "please-enter-data": "请先粘贴订单数据！",
                "identified-as-fliggy": "已识别为Fliggy订单，正在跳转...",
                "identified-as-kkday": "已识别为Kkday订单，正在跳转...",
                "identified-as-jingge": "已识别为Jing Ge OTA订单，正在跳转...",
                "identified-as-chong": "已识别为Chong Dealer订单，正在跳转...",
                "identified-as-ctrip": "已识别为携程专车订单，正在跳转...",
                "cannot-identify": "无法识别订单类型，请手动选择处理页面。",
                "returned-to-home": "已返回主页"
            }
        };

        // 更新所有UI文本
        function updateUILanguage(lang) {
            // 更新标题
            document.title = i18n[lang]["app-title"];
            
            // 更新data-i18n属性的元素
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (i18n[lang][key]) {
                    element.textContent = i18n[lang][key];
                }
            });
            
            // 更新占位符
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (i18n[lang][key]) {
                    element.placeholder = i18n[lang][key];
                }
            });
            
            // 保存当前语言到localStorage
            localStorage.setItem('preferred-language', lang);
        }

        // 规则定义 - 重构后的Jing Ge识别规则
        const otaRules = [
            {
                id: 1,
                provider: 'Fliggy',
                identification: 'fliggy, 订单编号, 买家, 支付时间',
                patterns: ['订单编号：\\d{19}', '买家：', '支付时间', '查看详情', '商家实收'],
                minMatches: 3
            },
            {
                id: 2,
                provider: 'Kkday',
                identification: 'kkday, 订单编号, 私人机场接送, 成本金额',
                patterns: ['订单编号 : \\w+', '私人机场接送', '成本金额', 'Booking Reference Number', 'Private Airport Transfer', 'Cost'],
                minMatches: 3
            },
            {
                id: 3,
                provider: 'Jing Ge',
                identification: 'jingge, 商铺订单, 订单号(可选闲鱼), 航班号, Customer Name',
                patterns: [
                    '商铺订单\\d+元',           // 商铺订单162元
                    '订单号(?:闲鱼)?\\d{10,25}', // 订单号闲鱼47474659036202889545971 或 订单号47474659036202889545971
                    '航班号\\s*\\(?[A-Z0-9]{2,7}\\)?', // 航班号 (AK117)
                    'Customer Name:\\s*[^\\n]+', // Customer Name: jack
                    '日期:\\s*\\d{4}-\\d{2}-\\d{2}', // 日期: 2025-05-19
                    '时间[:\\s]*\\d{1,2}:\\d{2}', // 时间:02:10:
                    '行程:\\s*[^\\n]+',         // 行程:T2-吉隆坡
                    '地址:\\s*[^\\n]+',         // 地址: Zero Healthcare...
                    '(吉隆坡|新加坡|槟城|新山|沙巴).*(接机|送机|包车)', // 地区+服务类型
                    '\\d+座车'                  // 5座车
                ],
                minMatches: 3  // 至少匹配3个模式即可识别为Jing Ge订单
            },
            {
                id: 4,
                provider: 'Chong',
                identification: 'chong, OTA: Tee, dealer',
                patterns: ['OTA: Tee', '接机', '送机', '联系人', '车型'],
                minMatches: 3
            },
            {
                id: 5,
                provider: 'Ctrip',
                identification: 'ctrip, 携程专车, 接单车型, 航班落地时间',
                patterns: ['接单车型', '乘客英文名', '乘客电话', '航班落地时间', '服务类型', '订单号'],
                minMatches: 3
            }
        ];

        // DOM元素引用
        const rawOrderTextarea = document.getElementById('raw-order');
        const identifyBtn = document.getElementById('identify-btn');
        const resetBtn = document.getElementById('reset-btn');
        const notification = document.getElementById('notification');
        const languageSelector = document.getElementById('language-selector');

        /**
         * @function identifyOrder - 重构后的订单识别函数
         * @description 识别订单类型并跳转到对应页面，包含详细的调试日志
         */
        function identifyOrder() {
            const orderText = rawOrderTextarea.value.trim();

            if (!orderText) {
                const currentLang = languageSelector.value;
                showNotification(i18n[currentLang]['please-enter-data']);
                return;
            }

            console.log('开始订单识别流程...');
            console.log('订单文本长度:', orderText.length);
            console.log('订单文本内容:', orderText);

            // 尝试匹配规则
            for (const rule of otaRules) {
                console.log(`\n检查规则: ${rule.provider} (ID: ${rule.id})`);
                console.log('识别特征:', rule.identification);
                console.log('匹配模式:', rule.patterns);

                let matchCount = 0;
                const matchedPatterns = [];
                const requiredMatches = rule.minMatches || Math.min(3, rule.patterns.length);

                for (const pattern of rule.patterns) {
                    try {
                        const regex = new RegExp(pattern, 'i');
                        if (regex.test(orderText)) {
                            matchCount++;
                            matchedPatterns.push(pattern);
                            console.log(`✅ 匹配成功: ${pattern}`);
                        } else {
                            console.log(`❌ 匹配失败: ${pattern}`);
                        }
                    } catch (error) {
                        console.error(`正则表达式错误: ${pattern}`, error);
                    }
                }

                console.log(`匹配结果: ${matchCount}/${rule.patterns.length} (需要: ${requiredMatches})`);
                console.log('匹配的模式:', matchedPatterns);

                // 如果匹配数达到阈值，跳转到对应页面
                if (matchCount >= requiredMatches) {
                    console.log(`🎯 识别成功! 订单类型: ${rule.provider}`);

                    // 特殊处理Jing Ge的文件名
                    let fileName;
                    if (rule.provider === 'Jing Ge') {
                        fileName = 'Jing Ge.html';
                    } else {
                        fileName = `${rule.provider.toLowerCase()}.html`;
                    }

                    const currentLang = languageSelector.value;
                    const providerKey = rule.provider.toLowerCase().replace(/\s+/g, '');
                    const notificationKey = `identified-as-${providerKey}`;

                    console.log('跳转文件名:', fileName);
                    console.log('通知键:', notificationKey);

                    showNotification(i18n[currentLang][notificationKey] || `已识别为${rule.provider}订单，正在跳转...`);

                    // 使用sessionStorage存储数据，避免URL过长
                    sessionStorage.setItem('orderData', orderText);
                    sessionStorage.setItem('orderProvider', rule.provider);

                    console.log('数据已存储到sessionStorage');

                    // 设置延时跳转，让用户看到通知
                    setTimeout(() => {
                        console.log(`正在跳转到: ${fileName}`);
                        window.location.href = fileName;
                    }, 1500);

                    return;
                }
            }

            // 如果没有匹配到任何规则
            console.log('❌ 无法识别订单类型');
            console.log('建议用户手动选择处理页面');

            const currentLang = languageSelector.value;
            showNotification(i18n[currentLang]['cannot-identify']);
        }

        // 显示通知
        function showNotification(message) {
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 事件监听器
        identifyBtn.addEventListener('click', identifyOrder);
        
        resetBtn.addEventListener('click', function() {
            rawOrderTextarea.value = '';
        });

        // 语言选择器事件
        languageSelector.addEventListener('change', function() {
            const selectedLang = this.value;
            updateUILanguage(selectedLang);
        });

        // 检查URL参数，如果有返回操作
        document.addEventListener('DOMContentLoaded', function() {
            // 设置初始语言
            const savedLang = localStorage.getItem('preferred-language') || 'zh';
            languageSelector.value = savedLang;
            updateUILanguage(savedLang);
            
            const urlParams = new URLSearchParams(window.location.search);
            const action = urlParams.get('action');
            
            if (action === 'return') {
                const currentLang = languageSelector.value;
                showNotification(i18n[currentLang]['returned-to-home']);
            }
        });
    </script>
</body>
</html>
