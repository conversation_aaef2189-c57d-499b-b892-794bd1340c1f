# Netlify MCP配置指南

## 📋 概述

本文档指导如何将 Order Rephase 项目配置到 Netlify MCP 系统中，实现更好的部署管理和环境变量配置。

## 🔧 MCP配置

### 1. 更新MCP配置文件

在您的 `~/.cursor/mcp.json` 文件中更新Netlify配置：

```json
{
  "mcpServers": {
    "Netlify": {
      "command": "npx -y @netlify/mcp",
      "env": {
        "NETLIFY_AUTH_TOKEN": "your-netlify-auth-token",
        "NETLIFY_SITE_ID": "your-site-id"
      }
    }
  }
}
```

### 2. 获取必要的令牌和ID

#### Netlify认证令牌
1. 登录到 [Netlify Dashboard](https://app.netlify.com/)
2. 转到 **User settings** > **Applications** > **Personal access tokens**
3. 点击 **New access token**
4. 输入描述并生成令牌
5. 复制令牌到MCP配置中

#### 网站ID
1. 在Netlify Dashboard中选择您的网站
2. 转到 **Site settings** > **General**
3. 在 **Site information** 部分找到 **Site ID**
4. 复制Site ID到MCP配置中

## 🌐 Netlify环境变量配置

### 1. 在Netlify仪表板设置环境变量

转到您的网站设置 > **Environment variables**，添加以下变量：

```bash
# API密钥（生产环境）
DEEPSEEK_API_KEY=your-production-deepseek-key
GEMINI_API_KEY=your-production-gemini-key

# 环境配置
NODE_ENV=production
APP_ENV=production

# 构建配置
NODE_VERSION=18
```

### 2. 环境变量安全说明

- ✅ **正确做法**：在Netlify仪表板中设置环境变量
- ❌ **错误做法**：在代码中硬编码生产环境API密钥
- 🔒 **安全提示**：生产环境API密钥永远不应该出现在代码仓库中

## 📦 项目配置文件

### netlify.toml 配置

项目当前的 `netlify.toml` 配置：

```toml
[build]
  publish = "."
  command = ""

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### api-config.js 更新

已更新的API配置文件支持：
- ✅ 自动环境检测（开发/生产）
- ✅ 安全的API密钥管理
- ✅ 配置验证功能
- ✅ 向后兼容性

## 🚀 部署流程

### 通过MCP部署

1. **连接到Netlify**
   ```bash
   # MCP会自动使用配置的认证信息
   ```

2. **查看部署状态**
   - 通过MCP监控部署进度
   - 查看构建日志
   - 检查环境变量配置

3. **验证部署**
   - 访问部署的网站
   - 检查浏览器控制台的环境日志
   - 确认API功能正常工作

### 本地开发测试

1. **启动本地服务器**
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx http-server -p 8000
   ```

2. **验证环境检测**
   - 打开浏览器开发者工具
   - 查看控制台日志确认环境为 "development"
   - 确认使用硬编码API密钥

## 🔍 故障排除

### 常见问题

1. **API密钥未正确配置**
   ```
   错误：[API-CONFIG] 生产环境配置问题: ['DeepSeek API密钥未正确配置']
   解决：在Netlify仪表板中设置正确的环境变量
   ```

2. **环境检测错误**
   ```
   问题：网站显示为开发环境但实际在生产环境
   解决：检查域名配置，确保包含 'netlify.app' 或 'netlify.com'
   ```

3. **MCP连接失败**
   ```
   问题：无法连接到Netlify
   解决：验证NETLIFY_AUTH_TOKEN和NETLIFY_SITE_ID是否正确
   ```

### 调试步骤

1. **检查环境变量**
   ```javascript
   // 在浏览器控制台运行
   console.log(window.API_CONFIG.environment);
   console.log(window.API_CONFIG.securityNote);
   ```

2. **验证API配置**
   ```javascript
   // 检查API密钥是否正确加载
   console.log(window.getDeepSeekApiKey());
   console.log(window.getGeminiApiKey());
   ```

3. **查看部署日志**
   - 在Netlify仪表板查看构建日志
   - 通过MCP监控部署状态

## 📊 监控和维护

### 定期检查项目

1. **环境变量更新**
   - 定期轮换API密钥
   - 更新环境变量配置

2. **部署监控**
   - 通过MCP监控部署状态
   - 设置部署失败通知

3. **性能优化**
   - 监控API调用统计
   - 优化加载时间

## 🔗 相关链接

- [Netlify MCP文档](https://docs.netlify.com/mcp/)
- [Netlify环境变量文档](https://docs.netlify.com/environment-variables/)
- [DeepSeek API文档](https://api.deepseek.com/docs)
- [Google Gemini API文档](https://ai.google.dev/gemini-api/docs)

## 📝 更新日志

- **v1.1.0** (2024-01-15): 增加环境检测和安全配置支持
- **v1.0.0** (2024-01-01): 初始MCP配置

---

**注意**：确保在生产环境中正确配置所有环境变量，以保证系统安全和功能正常。 