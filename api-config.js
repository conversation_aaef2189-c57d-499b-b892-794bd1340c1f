/**
 * @file API配置文件 - 集中管理所有API密钥和配置
 * @description 内部开发环境专用API密钥配置文件
 * @warning 此文件包含硬编码API密钥，仅用于内部开发和测试环境
 * @todo 生产环境部署时必须改为环境变量或安全配置文件方式
 */

// ==================== API密钥配置区域 ====================
// 注意：以下API密钥为硬编码方式，仅限内部开发使用

/**
 * DeepSeek API配置
 * @description 用于智能订单解析和地址翻译
 * @apiUrl https://api.deepseek.com/v1/chat/completions
 */
const DEEPSEEK_CONFIG = {
    apiKey: "***********************************", // 临时硬编码密钥
    baseUrl: "https://api.deepseek.com/v1",
    model: "deepseek-chat",
    timeout: 10000, // 10秒超时
    maxRetries: 2
};

/**
 * Gemini API配置
 * @description 用于酒店名称翻译和POI识别
 * @apiUrl https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent
 */
const GEMINI_CONFIG = {
    apiKey: "AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s", // 临时硬编码密钥
    baseUrl: "https://generativelanguage.googleapis.com/v1beta/models",
    model: "gemini-2.0-flash",
    timeout: 10000, // 10秒超时
    maxRetries: 2
};

/**
 * GoMyHire API配置
 * @description 用于订单创建和数据获取
 * @apiUrl https://staging.gomyhire.com.my/api
 */
const GOMYHIRE_CONFIG = {
    baseUrl: "https://staging.gomyhire.com.my/api",
    timeout: 15000, // 15秒超时
    endpoints: {
        backendUsers: "/backend_users",
        subCategories: "/sub_category",
        carTypes: "/car_types",
        createOrder: "/create_order"
    }
};

// ==================== API调用通用配置 ====================

/**
 * 通用请求配置
 */
const COMMON_CONFIG = {
    defaultTimeout: 10000,
    maxRetries: 2,
    retryDelay: 1000, // 重试延迟1秒
    headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'OrderRephase/1.0'
    }
};

/**
 * API调用统计配置
 */
const STATS_CONFIG = {
    enableLogging: true,
    logLevel: 'INFO', // DEBUG, INFO, WARN, ERROR
    trackApiCalls: true,
    trackResponseTime: true
};

// ==================== 导出配置对象 ====================

/**
 * 统一的API配置对象
 * @description 包含所有API的配置信息和通用设置
 */
window.API_CONFIG = {
    // API服务配置
    deepseek: DEEPSEEK_CONFIG,
    gemini: GEMINI_CONFIG,
    gomyhire: GOMYHIRE_CONFIG,
    
    // 通用配置
    common: COMMON_CONFIG,
    stats: STATS_CONFIG,
    
    // 环境标识
    environment: 'development', // development, staging, production
    version: '1.0.0',
    
    // 安全提醒
    securityNote: '此配置包含硬编码API密钥，仅用于内部开发环境'
};

// ==================== 便捷访问函数 ====================

/**
 * @function getDeepSeekApiKey - 获取DeepSeek API密钥
 * @returns {string} DeepSeek API密钥
 */
window.getDeepSeekApiKey = () => DEEPSEEK_CONFIG.apiKey;

/**
 * @function getGeminiApiKey - 获取Gemini API密钥
 * @returns {string} Gemini API密钥
 */
window.getGeminiApiKey = () => GEMINI_CONFIG.apiKey;

/**
 * @function getApiConfig - 获取指定API的完整配置
 * @param {string} apiName - API名称 (deepseek, gemini, gomyhire)
 * @returns {Object} API配置对象
 */
window.getApiConfig = (apiName) => {
    const configs = {
        deepseek: DEEPSEEK_CONFIG,
        gemini: GEMINI_CONFIG,
        gomyhire: GOMYHIRE_CONFIG
    };
    return configs[apiName] || null;
};

/**
 * @function logApiCall - 记录API调用日志
 * @param {string} apiName - API名称
 * @param {string} operation - 操作类型
 * @param {Object} details - 详细信息
 */
window.logApiCall = (apiName, operation, details = {}) => {
    if (!STATS_CONFIG.enableLogging) return;
    
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        api: apiName,
        operation,
        environment: 'development',
        ...details
    };
    
    console.log(`[API-CONFIG][${timestamp}][${apiName.toUpperCase()}] ${operation}`, logEntry);
};

// ==================== 初始化日志 ====================
console.log('[API-CONFIG] 配置文件已加载 - 开发环境模式');
console.warn('[API-CONFIG] 警告：当前使用硬编码API密钥，仅适用于内部开发环境');
